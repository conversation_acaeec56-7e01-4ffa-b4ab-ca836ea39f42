# Astrology Project

A comprehensive astrology platform offering horoscope readings, tarot card interpretations, numerology calculations, and personalized astrological insights.

## 🌟 Features

- **Horoscope Readings**
  - Daily, weekly, monthly, and yearly predictions
  - Personalized readings based on birth date and time
  - Zodiac sign compatibility analysis

- **Tarot Card Readings**
  - Single card, three-card, and Celtic Cross spreads
  - Detailed card interpretations
  - Interactive card selection interface

- **Numerology**
  - Life Path Number calculations
  - Destiny Number analysis
  - Personality Number insights
  - Soul Urge Number interpretations

- **User Features**
  - Personalized dashboard
  - Reading history
  - Premium subscription options
  - Profile customization

- **Additional Features**
  - Real-time chat support
  - Forum discussions
  - Blog articles
  - E-commerce shop
  - Premium content access

## 🚀 Getting Started

### Prerequisites

- Python 3.8+
- PostgreSQL
- Redis
- Node.js 14+
- Stripe account (for payments)
- OpenAI API key (for AI features)

### Environment Setup

1. Clone the repository:
```bash
git clone https://github.com/yourusername/astrology-project.git
cd astrology-project
```

2. Create and activate virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
cd frontend && npm install && cd ..
```

4. Create `.env` file:
```bash
cp .env.example .env
```

5. Configure environment variables in `.env`:
```
DJANGO_SECRET_KEY=your_secret_key
DEBUG=True
DB_NAME=astrology_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432
OPENAI_API_KEY=your_openai_key
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
```

### Database Setup

```bash
createdb astrology_db
python manage.py migrate
python manage.py createsuperuser
```

### Running the Application

1. Start Redis server:
```bash
redis-server
```

2. Start Celery worker and beat:
```bash
celery -A astrology_project worker -l info
celery -A astrology_project beat -l info
```

3. Run development server:
```bash
python manage.py runserver
```

4. Start frontend development server (optional):
```bash
cd frontend && npm start
```

## 📚 API Documentation

Access the API documentation at `/api/docs/` when running the server.

### Available Endpoints:

- `/api/zodiac-signs/`
- `/api/horoscope-readings/`
- `/api/personalized-readings/`
- `/api/tarot-cards/`
- `/api/numerology/`

## 🔒 Authentication

The application uses Django's authentication system with JWT tokens for API access.

### Premium Features

Premium features are accessible through subscription-based authentication:
- Enhanced readings
- Detailed interpretations
- Advanced compatibility analysis
- Priority support

## 💻 Technology Stack

- **Backend**
  - Django
  - Django REST Framework
  - Channels (WebSocket)
  - Celery
  - PostgreSQL
  - Redis

- **Frontend**
  - React
  - Apollo Client
  - Bootstrap
  - WebSocket

- **External Services**
  - Stripe (Payments)
  - OpenAI (AI Features)
  - AWS S3 (File Storage)
  - CloudFront (CDN)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## 👥 Authors

- Your Name - Initial work - [YourGithub](https://github.com/yourusername)

## 🙏 Acknowledgments

- Astrological data providers
- Open-source community
- Contributors and testers

## 📞 Support

For support, email <EMAIL> or join our Slack channel.