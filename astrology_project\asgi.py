import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from channels.auth import AuthMiddlewareStack
from chatbot.routing import websocket_urlpatterns

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'astrology_project.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            websocket_urlpatterns
        )
    ),
})