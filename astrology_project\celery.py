import os
from celery import Celery
from celery.schedules import crontab

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'astrology_project.settings')

app = Celery('astrology_project')
app.config_from_object('django.conf:settings', namespace='CELERY')
app.autodiscover_tasks()

# Configure periodic tasks
app.conf.beat_schedule = {
    'generate-daily-horoscopes': {
        'task': 'horoscopes.tasks.generate_daily_horoscopes',
        'schedule': crontab(hour=0, minute=0),
    },
    'generate-weekly-horoscopes': {
        'task': 'horoscopes.tasks.generate_weekly_horoscopes',
        'schedule': crontab(day_of_week='monday', hour=0, minute=0),
    },
    'generate-monthly-horoscopes': {
        'task': 'horoscopes.tasks.generate_monthly_horoscopes',
        'schedule': crontab(day_of_month='1', hour=0, minute=0),
    },
    'generate-yearly-horoscopes': {
        'task': 'horoscopes.tasks.generate_yearly_horoscopes',
        'schedule': crontab(month_of_year='1', day_of_month='1', hour=0, minute=0),
    },
    'send-horoscope-notifications': {
        'task': 'horoscopes.tasks.send_horoscope_notifications',
        'schedule': crontab(hour=8, minute=0),
    },
}
