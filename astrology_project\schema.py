import graphene
from graphene_django import DjangoObjectType
from horoscopes.models import ZodiacSign, HoroscopeReading
from tarot.models import TarotCard, TarotReading, TarotReadingCard
from compatibility.models import ZodiacCompatibility
from django.utils import timezone
import random

class ZodiacSignType(DjangoObjectType):
    class Meta:
        model = ZodiacSign

class HoroscopeReadingType(DjangoObjectType):
    class Meta:
        model = HoroscopeReading

class TarotCardType(DjangoObjectType):
    class Meta:
        model = TarotCard

class TarotReadingType(DjangoObjectType):
    class Meta:
        model = TarotReading

class TarotReadingCardType(DjangoObjectType):
    class Meta:
        model = TarotReadingCard

class DrawCardsInput(graphene.InputObjectType):
    spread_type = graphene.String(required=True)
    question = graphene.String(required=False)

class DrawCardsPayload(graphene.ObjectType):
    reading = graphene.Field(TarotReadingType)
    cards = graphene.List(TarotReadingCardType)

class Query(graphene.ObjectType):
    zodiac_sign = graphene.Field(ZodiacSignType, name=graphene.String())
    all_zodiac_signs = graphene.List(ZodiacSignType)
    daily_horoscope = graphene.Field(HoroscopeReadingType, sign=graphene.String())
    tarot_card = graphene.Field(TarotCardType, name=graphene.String())
    
    def resolve_zodiac_sign(self, info, name):
        return ZodiacSign.objects.get(name=name)
    
    def resolve_all_zodiac_signs(self, info):
        return ZodiacSign.objects.all()
    
    def resolve_daily_horoscope(self, info, sign):
        return HoroscopeReading.objects.filter(
            zodiac_sign__name=sign,
            reading_type='daily'
        ).latest('date')
    
    def resolve_tarot_card(self, info, name):
        return TarotCard.objects.get(name=name)

class Mutation(graphene.ObjectType):
    draw_cards = graphene.Field(
        DrawCardsPayload,
        input=DrawCardsInput(required=True)
    )

    def resolve_draw_cards(self, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        spread_type = input.spread_type
        num_cards = {
            'single': 1,
            'three_card': 3,
            'celtic_cross': 10
        }.get(spread_type)

        if not num_cards:
            raise Exception("Invalid spread type")

        # Create new reading
        reading = TarotReading.objects.create(
            user=user,
            spread_type=spread_type,
            question=input.get('question', ''),
            created_at=timezone.now()
        )

        # Draw cards
        all_cards = list(TarotCard.objects.all())
        drawn_cards = random.sample(all_cards, num_cards)
        reading_cards = []

        for position, card in enumerate(drawn_cards, 1):
            is_reversed = random.choice([True, False])
            reading_card = TarotReadingCard.objects.create(
                reading=reading,
                card=card,
                position=position,
                is_reversed=is_reversed,
                position_meaning=get_position_meaning(spread_type, position)
            )
            reading_cards.append(reading_card)

        # Generate interpretation
        reading.interpretation = generate_reading_interpretation(reading_cards)
        reading.save()

        return DrawCardsPayload(reading=reading, cards=reading_cards)

schema = graphene.Schema(query=Query, mutation=Mutation)
