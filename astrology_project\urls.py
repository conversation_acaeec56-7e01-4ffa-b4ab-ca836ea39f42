from django.contrib import admin
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from horoscopes.views import ZodiacSignViewSet, HoroscopeReadingViewSet, PersonalizedReadingViewSet

router = DefaultRouter()
router.register(r'zodiac-signs', ZodiacSignViewSet)
router.register(r'horoscope-readings', HoroscopeReadingViewSet, basename='horoscope-reading')
router.register(r'personalized-readings', PersonalizedReadingViewSet, basename='personalized-reading')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
    path('', include('core.urls')),
    path('horoscopes/', include('horoscopes.urls')),
    path('users/', include('users.urls')),
    path('shop/', include('shop.urls')),
    path('forum/', include('forum.urls')),
    path('blog/', include('blog.urls')),
    path('accounts/', include('allauth.urls')),
    path('moderation/', include('moderation.urls')),
    path('compatibility/', include('compatibility.urls')),
]
