from django.contrib import admin
from django.utils.html import format_html
from .models import Post, Comment

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ['author', 'post', 'truncated_content', 'created_at', 'is_approved', 'is_reply', 'actions_buttons']
    list_filter = ['is_approved', 'created_at']
    search_fields = ['content', 'author__username', 'post__title']
    actions = ['approve_comments', 'unapprove_comments']
    readonly_fields = ['created_at', 'updated_at']
    raw_id_fields = ['author', 'post', 'parent']

    def truncated_content(self, obj):
        return obj.content[:100] + '...' if len(obj.content) > 100 else obj.content
    truncated_content.short_description = 'Content'

    def is_reply(self, obj):
        return bool(obj.parent)
    is_reply.boolean = True
    is_reply.short_description = 'Reply'

    def actions_buttons(self, obj):
        if obj.is_approved:
            button = f'<a class="button" href="?action=unapprove&id={obj.id}">Unapprove</a>'
        else:
            button = f'<a class="button" href="?action=approve&id={obj.id}">Approve</a>'
        return format_html(button)
    actions_buttons.short_description = 'Actions'
    actions_buttons.allow_tags = True

    def approve_comments(self, request, queryset):
        queryset.update(is_approved=True)
    approve_comments.short_description = "Approve selected comments"

    def unapprove_comments(self, request, queryset):
        queryset.update(is_approved=False)
    unapprove_comments.short_description = "Unapprove selected comments"

@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ['title', 'author', 'status', 'created_at', 'comment_count']
    list_filter = ['status', 'created_at', 'author']
    search_fields = ['title', 'content', 'author__username']
    prepopulated_fields = {'slug': ('title',)}
    raw_id_fields = ['author']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    actions = ['publish_posts', 'unpublish_posts']

    def comment_count(self, obj):
        return obj.comments.count()
    comment_count.short_description = 'Comments'

    def publish_posts(self, request, queryset):
        queryset.update(status='published')
    publish_posts.short_description = "Publish selected posts"

    def unpublish_posts(self, request, queryset):
        queryset.update(status='draft')
    unpublish_posts.short_description = "Unpublish selected posts"

    fieldsets = (
        (None, {
            'fields': ('title', 'slug', 'author', 'content')
        }),
        ('Publishing', {
            'fields': ('status', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('SEO', {
            'fields': ('meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
    )