from django.contrib.syndication.views import Feed
from django.urls import reverse_lazy
from django.utils.feedgenerator import Rss201rev2Feed
from .models import Post

class ExtendedRSSFeed(Rss201rev2Feed):
    def add_item_elements(self, handler, item):
        super().add_item_elements(handler, item)
        if item['author_name'] is not None:
            handler.addQuickElement('author', item['author_name'])
        if item['categories'] is not None:
            for category in item['categories']:
                handler.addQuickElement('category', category)

class LatestPostsFeed(Feed):
    feed_type = ExtendedRSSFeed
    title = "Astrology Blog Latest Posts"
    link = reverse_lazy('blog:post_list')
    description = "Latest posts from our astrology blog"

    def items(self):
        return Post.objects.filter(status='published').order_by('-published_at')[:10]

    def item_title(self, item):
        return item.title

    def item_description(self, item):
        return item.excerpt or item.content[:200]

    def item_author_name(self, item):
        return item.author.get_full_name() or item.author.username

    def item_categories(self, item):
        return [item.category.name] + [tag.name for tag in item.tags.all()]

    def item_pubdate(self, item):
        return item.published_at

    def item_link(self, item):
        return reverse_lazy('blog:post_detail', args=[item.slug])