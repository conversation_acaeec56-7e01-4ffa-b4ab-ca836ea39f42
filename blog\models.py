from django.db import models
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils.text import slugify
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
import bleach

User = get_user_model()

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = 'Categories'
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('blog:category_detail', kwargs={'slug': self.slug})

class Tag(models.Model):
    name = models.CharField(max_length=50)
    slug = models.SlugField(unique=True)

    def __str__(self):
        return self.name

class Post(models.Model):
    STATUS_CHOICES = (
        ('draft', 'Draft'),
        ('pending', 'Pending Review'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    )
    
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='blog_posts')
    content = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    meta_description = models.CharField(max_length=160, blank=True)
    meta_keywords = models.CharField(max_length=255, blank=True)
    featured_image = models.ImageField(upload_to='blog/images/', null=True, blank=True)
    views_count = models.PositiveIntegerField(default=0)
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('blog:post_detail', args=[self.slug])

class Comment(models.Model):
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='replies')
    
    # Moderation fields
    is_spam = models.BooleanField(default=False)
    reported_by = models.ManyToManyField(User, related_name='reported_comments', blank=True)
    moderation_notes = models.TextField(blank=True)
    moderation_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending Review'),
            ('approved', 'Approved'),
            ('rejected', 'Rejected'),
            ('spam', 'Marked as Spam')
        ],
        default='pending'
    )

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f'Comment by {self.author.username} on {self.post.title}'

    def save(self, *args, **kwargs):
        # Clean and sanitize content
        allowed_tags = ['p', 'br', 'strong', 'em', 'a']
        allowed_attributes = {'a': ['href', 'title']}
        self.content = bleach.clean(
            self.content,
            tags=allowed_tags,
            attributes=allowed_attributes,
            strip=True
        )

        # Auto-approve comments from trusted users
        if self.author.is_staff or self.author.is_premium:
            self.is_approved = True
            self.moderation_status = 'approved'

        super().save(*args, **kwargs)

    def report(self, user):
        if user != self.author and user not in self.reported_by.all():
            self.reported_by.add(user)
            if self.reported_by.count() >= 3:
                self.is_approved = False
                self.moderation_status = 'pending'
                self.moderation_notes = "Automatically flagged due to multiple reports"
                self.save()
                self.notify_moderators()

    def notify_moderators(self):
        moderators = User.objects.filter(is_staff=True)
        context = {
            'comment': self,
            'reports_count': self.reported_by.count(),
            'post_url': self.post.get_absolute_url()
        }
        
        message = render_to_string('blog/emails/comment_reported.html', context)
        
        send_mail(
            subject=f'Comment reported on: {self.post.title}',
            message=message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[mod.email for mod in moderators],
            fail_silently=True
        )

    def send_notification_email(self):
        """Send notification about new comment"""
        if not self.is_approved:
            return  # Don't send notifications for unapproved comments
            
        # Notify post author about new comment
        context = {
            'post': self.post,
            'comment': self,
            'author': self.author,
        }
        
        subject = f'New comment on your post: {self.post.title}'
        message = render_to_string('blog/emails/new_comment.html', context)
        
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [self.post.author.email],
            fail_silently=True,
            html_message=message
        )

        # Notify parent comment author if this is a reply
        if self.parent and self.parent.author != self.author:
            reply_context = {
                'post': self.post,
                'comment': self,
                'parent_comment': self.parent,
            }
            
            reply_subject = f'Someone replied to your comment on: {self.post.title}'
            reply_message = render_to_string('blog/emails/comment_reply.html', reply_context)
            
            send_mail(
                reply_subject,
                reply_message,
                settings.DEFAULT_FROM_EMAIL,
                [self.parent.author.email],
                fail_silently=True,
                html_message=reply_message
            )
