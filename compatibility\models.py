from django.db import models
from horoscopes.models import ZodiacSign

class ZodiacCompatibility(models.Model):
    sign1 = models.ForeignKey(ZodiacSign, on_delete=models.CASCADE, related_name='compatibility_as_first')
    sign2 = models.ForeignKey(ZodiacSign, on_delete=models.CASCADE, related_name='compatibility_as_second')
    love_score = models.IntegerField(default=0)
    friendship_score = models.IntegerField(default=0)
    work_score = models.IntegerField(default=0)
    description = models.TextField()
    
    class Meta:
        unique_together = ['sign1', 'sign2']
        
    @property
    def overall_score(self):
        return (self.love_score + self.friendship_score + self.work_score) // 3