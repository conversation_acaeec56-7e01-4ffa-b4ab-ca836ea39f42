from django.views.generic import TemplateView, ListView
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from .models import ZodiacCompatibility
from horoscopes.models import ZodiacSign

class CompatibilityCheckerView(TemplateView):
    template_name = 'compatibility/checker.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        sign1 = self.request.GET.get('sign1')
        sign2 = self.request.GET.get('sign2')
        
        if sign1 and sign2:
            sign1_obj = get_object_or_404(ZodiacSign, name=sign1)
            sign2_obj = get_object_or_404(ZodiacSign, name=sign2)
            
            compatibility = ZodiacCompatibility.objects.filter(
                sign1=sign1_obj, 
                sign2=sign2_obj
            ).first()
            
            if not compatibility:
                compatibility = ZodiacCompatibility.objects.filter(
                    sign1=sign2_obj,
                    sign2=sign1_obj
                ).first()
            
            context['compatibility'] = compatibility
            
        context['zodiac_signs'] = ZodiacSign.objects.all()
        return context

class LoveMatchesView(ListView):
    template_name = 'compatibility/love_matches.html'
    context_object_name = 'matches'
    
    def get_queryset(self):
        user_sign = self.request.user.zodiac_sign
        if user_sign:
            return ZodiacCompatibility.objects.filter(
                sign1=user_sign,
                love_score__gte=70
            ).order_by('-love_score')
        return ZodiacCompatibility.objects.none()

class FriendshipCompatibilityView(ListView):
    template_name = 'compatibility/friendship.html'
    context_object_name = 'matches'
    
    def get_queryset(self):
        user_sign = self.request.user.zodiac_sign
        if user_sign:
            return ZodiacCompatibility.objects.filter(
                sign1=user_sign,
                friendship_score__gte=70
            ).order_by('-friendship_score')
        return ZodiacCompatibility.objects.none()

class WorkCompatibilityView(ListView):
    template_name = 'compatibility/work.html'
    context_object_name = 'matches'
    
    def get_queryset(self):
        user_sign = self.request.user.zodiac_sign
        if user_sign:
            return ZodiacCompatibility.objects.filter(
                sign1=user_sign,
                work_score__gte=70
            ).order_by('-work_score')
        return ZodiacCompatibility.objects.none()

def calculate_compatibility(request):
    sign1 = request.GET.get('sign1')
    sign2 = request.GET.get('sign2')
    
    if not (sign1 and sign2):
        return JsonResponse({'error': 'Both signs are required'}, status=400)
        
    compatibility = ZodiacCompatibility.objects.filter(
        sign1__name=sign1,
        sign2__name=sign2
    ).first()
    
    if not compatibility:
        compatibility = ZodiacCompatibility.objects.filter(
            sign1__name=sign2,
            sign2__name=sign1
        ).first()
    
    if not compatibility:
        return JsonResponse({'error': 'Compatibility not found'}, status=404)
    
    return JsonResponse({
        'love_score': compatibility.love_score,
        'friendship_score': compatibility.friendship_score,
        'work_score': compatibility.work_score,
        'description': compatibility.description
    })
