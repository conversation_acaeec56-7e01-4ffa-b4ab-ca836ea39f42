from django.urls import path
from . import views

app_name = 'forum'

urlpatterns = [
    path('', views.CategoryListView.as_view(), name='category_list'),
    path('<slug:slug>/', views.CategoryDetailView.as_view(), name='category_detail'),
    path('<slug:category_slug>/new-topic/', views.TopicCreateView.as_view(), name='topic_create'),
    path('<slug:category_slug>/<slug:slug>/', views.TopicDetailView.as_view(), name='topic_detail'),
    path('<slug:category_slug>/<slug:topic_slug>/reply/', views.ReplyCreateView.as_view(), name='reply_create'),
]