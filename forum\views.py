from django.views.generic import List<PERSON>ie<PERSON>, DetailView, Create<PERSON>iew, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from .models import Category, Topic, Reply
from .forms import TopicForm, ReplyForm

class CategoryListView(ListView):
    model = Category
    context_object_name = 'categories'
    template_name = 'forum/category_list.html'

class CategoryDetailView(DetailView):
    model = Category
    context_object_name = 'category'
    template_name = 'forum/category_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['topics'] = self.object.topics.all()
        return context

class TopicCreateView(LoginRequiredMixin, CreateView):
    model = Topic
    form_class = TopicForm
    template_name = 'forum/topic_form.html'

    def form_valid(self, form):
        form.instance.author = self.request.user
        form.instance.category = get_object_or_404(Category, slug=self.kwargs['category_slug'])
        return super().form_valid(form)

class TopicDetailView(DetailView):
    model = Topic
    context_object_name = 'topic'
    template_name = 'forum/topic_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['replies'] = self.object.replies.all()
        context['reply_form'] = ReplyForm()
        return context

    def get(self, request, *args, **kwargs):
        response = super().get(request, *args, **kwargs)
        self.object.views_count += 1
        self.object.save()
        return response

class ReplyCreateView(LoginRequiredMixin, CreateView):
    model = Reply
    form_class = ReplyForm
    template_name = 'forum/reply_form.html'

    def form_valid(self, form):
        form.instance.author = self.request.user
        form.instance.topic = get_object_or_404(Topic, slug=self.kwargs['topic_slug'])
        return super().form_valid(form)

    def get_success_url(self):
        return self.object.topic.get_absolute_url()