import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import NumerologyCalculator from '../pages/NumerologyCalculator';
import { CALCULATE_NUMEROLOGY } from '../graphql/queries';

const mocks = [
  {
    request: {
      query: CALCULATE_NUMEROLOGY,
      variables: {
        input: {
          fullName: "John Doe",
          birthDate: "1990-01-01",
        },
      },
    },
    result: {
      data: {
        calculateNumerology: {
          life_path_number: 3,
          destiny_number: 7,
          soul_urge_number: 5,
          personality_number: 4,
          birth_day_number: 1,
          current_year_number: 6,
          reading: {
            id: "1",
            reading_type: "life_path",
            number: 3,
            interpretation: "Test interpretation",
            created_at: "2023-01-01T00:00:00Z",
          },
        },
      },
    },
  },
];

describe('NumerologyCalculator', () => {
  test('renders form fields', () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <NumerologyCalculator />
      </MockedProvider>
    );

    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/birth date/i)).toBeInTheDocument();
  });

  test('shows error message for empty fields', async () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <NumerologyCalculator />
      </MockedProvider>
    );

    fireEvent.click(screen.getByText(/calculate numbers/i));
    
    expect(await screen.findByText(/please fill in all fields/i)).toBeInTheDocument();
  });

  test('calculates numerology successfully', async () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <NumerologyCalculator />
      </MockedProvider>
    );

    fireEvent.change(screen.getByLabelText(/full name/i), {
      target: { value: 'John Doe' },
    });
    fireEvent.change(screen.getByLabelText(/birth date/i), {
      target: { value: '1990-01-01' },
    });
    fireEvent.click(screen.getByText(/calculate numbers/i));

    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  test('displays loading state', async () => {
    render(
      <MockedProvider mocks={mocks} addTypename={false}>
        <NumerologyCalculator />
      </MockedProvider>
    );

    fireEvent.change(screen.getByLabelText(/full name/i), {
      target: { value: 'John Doe' },
    });
    fireEvent.change(screen.getByLabelText(/birth date/i), {
      target: { value: '1990-01-01' },
    });
    fireEvent.click(screen.getByText(/calculate numbers/i));

    expect(screen.getByText(/calculating/i)).toBeInTheDocument();
  });
});