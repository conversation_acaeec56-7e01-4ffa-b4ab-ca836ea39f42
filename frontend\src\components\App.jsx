import React from 'react';
import { <PERSON>rowser<PERSON>outer as Router, Route, Switch } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import Navbar from './components/Navbar';
import Dashboard from './pages/Dashboard';
import HoroscopeReader from './pages/HoroscopeReader';
import TarotReader from './pages/TarotReader';
import NumerologyCalculator from './pages/NumerologyCalculator';
import AstrologyChart from './components/AstrologyChart';
import ChatBot from './components/ChatBot';
import Profile from './pages/Profile';

const App = () => {
  return (
    <ThemeProvider>
      <Router>
        <Navbar />
        <Switch>
          <Route exact path="/" component={Dashboard} />
          <Route path="/horoscope" component={HoroscopeReader} />
          <Route path="/tarot" component={TarotReader} />
          <Route path="/numerology" component={NumerologyCalculator} />
          <Route path="/chart" component={Astrology<PERSON>hart} />
          <Route path="/chat" component={ChatBot} />
          <Route path="/profile" component={Profile} />
        </Switch>
      </Router>
    </ThemeProvider>
  );
};

export default App;