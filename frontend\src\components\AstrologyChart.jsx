import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const AstrologyChart = ({ birthData }) => {
    const chartRef = useRef();

    useEffect(() => {
        if (!birthData) return;

        const width = 800;
        const height = 800;
        const radius = Math.min(width, height) / 2;

        // Clear previous chart
        d3.select(chartRef.current).selectAll("*").remove();

        // Create SVG
        const svg = d3.select(chartRef.current)
            .append("svg")
            .attr("width", width)
            .attr("height", height)
            .append("g")
            .attr("transform", `translate(${width/2},${height/2})`);

        // Draw zodiac wheel
        const zodiacWheel = svg.append("g").attr("class", "zodiac-wheel");

        // Draw houses
        const houses = svg.append("g").attr("class", "houses");

        // Draw planets
        const planets = svg.append("g").attr("class", "planets");

        // Draw aspects
        const aspects = svg.append("g").attr("class", "aspects");

        // Add interactivity
        addInteractiveElements(svg, birthData);
    }, [birthData]);

    const addInteractiveElements = (svg, data) => {
        // Add zoom functionality
        const zoom = d3.zoom()
            .scaleExtent([1, 4])
            .on("zoom", (event) => {
                svg.attr("transform", event.transform);
            });

        svg.call(zoom);

        // Add tooltips
        const tooltip = d3.select("body")
            .append("div")
            .attr("class", "tooltip")
            .style("opacity", 0);

        // Add click handlers
        svg.selectAll(".planet")
            .on("click", (event, d) => {
                // Handle planet click
            })
            .on("mouseover", (event, d) => {
                tooltip.transition()
                    .duration(200)
                    .style("opacity", .9);
                tooltip.html(getPlanetInfo(d))
                    .style("left", (event.pageX) + "px")
                    .style("top", (event.pageY - 28) + "px");
            })
            .on("mouseout", (d) => {
                tooltip.transition()
                    .duration(500)
                    .style("opacity", 0);
            });
    };

    return (
        <div className="astrology-chart-container">
            <div ref={chartRef}></div>
            <div className="chart-controls">
                <button onClick={() => handleChartAction('houses')}>Toggle Houses</button>
                <button onClick={() => handleChartAction('aspects')}>Toggle Aspects</button>
                <button onClick={() => handleChartAction('transit')}>Show Transits</button>
            </div>
        </div>
    );
};

export default AstrologyChart;