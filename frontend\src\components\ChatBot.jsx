import React, { useState, useEffect, useRef } from 'react';
import { useMutation, useSubscription } from '@apollo/client';
import { SEND_MESSAGE, MESSAGE_SUBSCRIPTION } from '../graphql/queries';

const ChatBot = () => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);

  const [sendMessage] = useMutation(SEND_MESSAGE);
  
  const { data: messageData } = useSubscription(MESSAGE_SUBSCRIPTION);

  useEffect(() => {
    if (messageData) {
      const newMessage = messageData.messageReceived;
      setMessages(prev => [...prev, newMessage]);
    }
  }, [messageData]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    try {
      await sendMessage({
        variables: {
          content: input,
          sessionId: sessionStorage.getItem('chatSessionId')
        }
      });
      setInput('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <div className="chatbot-container">
      <div className="messages-container">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`message ${message.messageType === 'user' ? 'user' : 'bot'}`}
          >
            <p>{message.content}</p>
            <small>{new Date(message.timestamp).toLocaleTimeString()}</small>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <form onSubmit={handleSubmit} className="input-form">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask about astrology, tarot, or numerology..."
        />
        <button type="submit">Send</button>
      </form>
    </div>
  );
};

export default ChatBot;