import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const numberMeanings = {
  life_path: {
    1: "Natural born leader. Independent, ambitious, and innovative. You're driven to achieve and pioneer new paths.",
    2: "Diplomatic and cooperative. You're a peacemaker with strong intuition and emotional intelligence.",
    3: "Creative and expressive. You have natural artistic talents and bring joy to others through communication.",
    4: "Practical and hardworking. You're a builder who creates solid foundations and values stability.",
    5: "Freedom-loving and versatile. You embrace change and seek adventure and new experiences.",
    6: "Nurturing and responsible. You're a natural caregiver with strong sense of harmony and balance.",
    7: "Analytical and spiritual. You seek knowledge and truth through research and introspection.",
    8: "Ambitious and successful. You have natural business acumen and leadership abilities.",
    9: "Humanitarian and compassionate. You're here to serve humanity and make a global impact.",
    11: "Master number - Spiritual messenger. Highly intuitive with potential for spiritual enlightenment.",
    22: "Master number - Master builder. You can turn dreams into reality on a large scale.",
    33: "Master number - Master teacher. You have great capacity for healing and uplifting others."
  },
  destiny: {
    1: "Born to be a pioneer and leader. Your destiny is to forge new paths and inspire others.",
    2: "Destined to be a mediator and diplomat. You bring harmony and cooperation.",
    3: "Your destiny involves creative self-expression and inspiring others through art or words.",
    4: "Destined to build lasting foundations and create order from chaos.",
    5: "Your destiny involves bringing positive change and freedom to others.",
    6: "Destined to nurture and protect, creating harmony in home and community.",
    7: "Your destiny involves seeking and sharing wisdom and spiritual truth.",
    8: "Destined for material success and power used for the greater good.",
    9: "Your destiny involves humanitarian service and universal love.",
    11: "Destined to be a spiritual illuminator and inspire mass consciousness.",
    22: "Destined to create large-scale projects that benefit humanity.",
    33: "Destined to be a spiritual teacher and healer on a global scale."
  },
  soul_urge: {
    1: "Deep desire for independence and achievement. You want to lead and pioneer.",
    2: "Inner need for harmony and connection. You seek peace and cooperation.",
    3: "Soul's desire for creative expression and joy. You need to share your creativity.",
    4: "Inner need for stability and order. You seek security and solid foundations.",
    5: "Deep desire for freedom and adventure. You need variety and change.",
    6: "Soul's desire to nurture and protect. You seek harmony in relationships.",
    7: "Inner need for wisdom and understanding. You seek spiritual truth.",
    8: "Deep desire for material success and power. You seek abundance.",
    9: "Soul's desire to serve humanity. You seek to make a difference.",
    11: "Deep desire for spiritual enlightenment and inspiring others.",
    22: "Inner need to create significant practical achievements.",
    33: "Soul's desire to teach and heal on a spiritual level."
  },
  personality: {
    1: "You appear confident, independent, and original to others.",
    2: "Others see you as diplomatic, cooperative, and sensitive.",
    3: "You come across as creative, expressive, and optimistic.",
    4: "Others perceive you as reliable, practical, and organized.",
    5: "You appear adventurous, versatile, and progressive.",
    6: "Others see you as responsible, caring, and harmonious.",
    7: "You come across as mysterious, analytical, and wise.",
    8: "Others perceive you as confident, successful, and powerful.",
    9: "You appear compassionate, sophisticated, and humanitarian.",
    11: "Others see you as inspirational and highly intuitive.",
    22: "You come across as masterful and highly capable.",
    33: "Others perceive you as nurturing and spiritually wise."
  }
};

const NumberMeaning = ({ number, type }) => {
  const meaning = numberMeanings[type]?.[number] || "This number requires personal interpretation.";
  
  return (
    <AnimatePresence mode="wait">
      <motion.div 
        className="number-meaning"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div 
          className="number-display"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 15 }}
        >
          <span className="number">{number}</span>
        </motion.div>
        
        <motion.div 
          className="meaning-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <p>{meaning}</p>
        </motion.div>
        
        {type === 'life_path' && (
          <motion.div 
            className="traits-list"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h4>Key Traits:</h4>
            <ul>
              {getTraits(number, type).map((trait, index) => (
                <motion.li 
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 + index * 0.1 }}
                >
                  {trait}
                </motion.li>
              ))}
            </ul>
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

const getTraits = (number, type) => {
  const traitsMap = {
    1: ["Leadership", "Independence", "Innovation", "Ambition"],
    2: ["Diplomacy", "Cooperation", "Intuition", "Harmony"],
    3: ["Creativity", "Expression", "Joy", "Communication"],
    4: ["Stability", "Organization", "Dedication", "Practicality"],
    5: ["Freedom", "Change", "Adventure", "Versatility"],
    6: ["Nurturing", "Responsibility", "Balance", "Love"],
    7: ["Analysis", "Wisdom", "Spirituality", "Research"],
    8: ["Power", "Success", "Achievement", "Authority"],
    9: ["Compassion", "Universal Love", "Wisdom", "Service"],
    11: ["Inspiration", "Illumination", "Intuition", "Vision"],
    22: ["Mastery", "Manifestation", "Power", "Achievement"],
    33: ["Teaching", "Healing", "Compassion", "Guidance"]
  };

  return traitsMap[number] || [];
};

export default NumberMeaning;
