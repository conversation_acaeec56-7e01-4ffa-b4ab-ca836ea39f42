import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import { SEND_CHAT_MESSAGE } from '../graphql/mutations';

const NumerologyChatbot = ({ numberType, number }) => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  
  const [sendMessage] = useMutation(SEND_CHAT_MESSAGE);

  const handleSendMessage = async () => {
    if (!input.trim()) return;

    const newMessage = {
      content: input,
      type: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInput('');

    try {
      const { data } = await sendMessage({
        variables: {
          message: input,
          context: {
            numberType,
            number
          }
        }
      });

      setMessages(prev => [...prev, {
        content: data.sendChatMessage.response,
        type: 'bot',
        timestamp: new Date()
      }]);
    } catch (error) {
      console.error('Chat error:', error);
    }
  };

  return (
    <div className="chatbot-container">
      <div className="chat-messages">
        {messages.map((msg, index) => (
          <div key={index} className={`message ${msg.type}`}>
            {msg.content}
          </div>
        ))}
      </div>
      <div className="chat-input">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask about your numbers..."
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <button onClick={handleSendMessage}>Send</button>
      </div>
    </div>
  );
};

export default NumerologyChatbot;