import React from 'react';
import { useMutation } from '@apollo/client';
import { EXPORT_NUMEROLOGY_DATA } from '../graphql/mutations';

const NumerologyExport = ({ readingData }) => {
  const [exportData, { loading }] = useMutation(EXPORT_NUMEROLOGY_DATA);

  const handleExport = async (format) => {
    try {
      const { data } = await exportData({
        variables: {
          readingId: readingData.id,
          format
        }
      });

      // Create and download file
      const blob = new Blob([data.exportNumerology.content], {
        type: format === 'PDF' ? 'application/pdf' : 'application/json'
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `numerology-reading-${new Date().toISOString()}.${format.toLowerCase()}`;
      a.click();
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  return (
    <div className="export-options">
      <button 
        onClick={() => handleExport('PDF')} 
        disabled={loading}
      >
        Export as PDF
      </button>
      <button 
        onClick={() => handleExport('JSON')} 
        disabled={loading}
      >
        Export as JSON
      </button>
    </div>
  );
};

export default NumerologyExport;