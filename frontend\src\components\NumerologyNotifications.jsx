import React, { useEffect } from 'react';
import { useSubscription } from '@apollo/client';
import { NUMEROLOGY_UPDATES } from '../graphql/subscriptions';

const NumerologyNotifications = () => {
  const { data } = useSubscription(NUMEROLOGY_UPDATES);

  useEffect(() => {
    if (data?.numerologyUpdate) {
      const { type, message } = data.numerologyUpdate;
      
      // Show notification
      if (Notification.permission === "granted") {
        new Notification("Numerology Update", {
          body: message,
          icon: "/path/to/icon.png"
        });
      }
    }
  }, [data]);

  return null; // This component handles background notifications
};

export default NumerologyNotifications;