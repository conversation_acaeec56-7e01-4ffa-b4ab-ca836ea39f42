import React from 'react';
import { useQuery } from '@apollo/client';
import { GET_USER_READINGS } from '../graphql/queries';
import LoadingSpinner from './LoadingSpinner';

const UserNumerologyHistory = () => {
  const { data, loading, error } = useQuery(GET_USER_READINGS);

  if (loading) return <LoadingSpinner />;
  if (error) return <div className="error-message">{error.message}</div>;

  return (
    <div className="reading-history">
      <h3>Your Past Readings</h3>
      {data.numerologyReadings.map(reading => (
        <div key={reading.id} className="reading-card">
          <div className="reading-date">
            {new Date(reading.created_at).toLocaleDateString()}
          </div>
          <div className="reading-type">{reading.reading_type}</div>
          <div className="reading-number">Number: {reading.number}</div>
          <p className="reading-interpretation">{reading.interpretation}</p>
        </div>
      ))}
    </div>
  );
};

export default UserNumerologyHistory;