import { gql } from '@apollo/client';

export const CALCULATE_NUMEROLOGY = gql`
  mutation CalculateNumerology($input: CalculateNumerologyInput!) {
    calculateNumerology(input: $input) {
      life_path_number
      destiny_number
      soul_urge_number
      personality_number
      birth_day_number
      current_year_number
      reading {
        id
        reading_type
        number
        interpretation
        created_at
      }
    }
  }
`;

export const GET_NUMEROLOGY_PROFILE = gql`
  query GetNumerologyProfile {
    numerologyProfile {
      id
      life_path_number
      destiny_number
      soul_urge_number
      personality_number
      birth_day_number
      current_year_number
    }
  }
`;

export const GET_NUMEROLOGY_READINGS = gql`
  query GetNumerologyReadings {
    numerologyReadings {
      id
      reading_type
      number
      interpretation
      created_at
    }
  }
`;