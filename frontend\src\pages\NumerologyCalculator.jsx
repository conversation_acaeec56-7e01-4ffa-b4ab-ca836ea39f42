import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import { CALCULATE_NUMEROLOGY } from '../graphql/queries';
import NumberMeaning from '../components/NumberMeaning';
import LoadingSpinner from '../components/LoadingSpinner';
import { motion, AnimatePresence } from 'framer-motion';

const NumerologyCalculator = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    birthDate: '',
  });
  const [results, setResults] = useState(null);
  const [activeTab, setActiveTab] = useState('life_path');
  const [error, setError] = useState(null);

  const [calculateNumerology, { loading }] = useMutation(CALCULATE_NUMEROLOGY, {
    onCompleted: (data) => {
      setResults(data.calculateNumerology);
      setError(null);
    },
    onError: (error) => {
      setError(error.message);
      setResults(null);
    },
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    
    if (!formData.fullName.trim() || !formData.birthDate) {
      setError('Please fill in all fields');
      return;
    }

    try {
      await calculateNumerology({
        variables: {
          input: {
            fullName: formData.fullName,
            birthDate: formData.birthDate,
          },
        },
      });
    } catch (err) {
      // Error is handled by onError callback
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError(null);
  };

  return (
    <div className="numerology-calculator">
      <motion.form 
        className="calculator-form"
        onSubmit={handleSubmit}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="form-group">
          <label htmlFor="fullName">Full Name</label>
          <input
            type="text"
            id="fullName"
            name="fullName"
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="Enter your full name"
          />
        </div>
        <div className="form-group">
          <label htmlFor="birthDate">Birth Date</label>
          <input
            type="date"
            id="birthDate"
            name="birthDate"
            value={formData.birthDate}
            onChange={handleInputChange}
          />
        </div>
        <button 
          type="submit" 
          className="submit-button"
          disabled={loading}
        >
          Calculate Numbers
        </button>
      </motion.form>

      <AnimatePresence>
        {error && (
          <motion.div 
            className="error-message"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            {error}
          </motion.div>
        )}
      </AnimatePresence>

      {loading && <LoadingSpinner />}

      {results && !loading && (
        <motion.div 
          className="results-section"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          <div className="tabs">
            {Object.keys(results).map(key => (
              key !== 'reading' && (
                <button
                  key={key}
                  className={activeTab === key ? 'active' : ''}
                  onClick={() => setActiveTab(key)}
                >
                  {key.replace('_', ' ').toUpperCase()}
                </button>
              )
            ))}
          </div>

          <NumberMeaning 
            number={results[activeTab]} 
            type={activeTab}
          />
        </motion.div>
      )}
    </div>
  );
};

export default NumerologyCalculator;
