import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { DRAW_CARDS, GET_TAROT_READING } from '../graphql/queries';

const TarotReader = () => {
  const [spread, setSpread] = useState('single');
  const [cards, setCards] = useState([]);
  const [question, setQuestion] = useState('');

  const { loading: readingLoading, data: readingData } = useQuery(GET_TAROT_READING, {
    variables: { id: sessionStorage.getItem('currentReadingId') }
  });

  const [drawCards] = useMutation(DRAW_CARDS, {
    onCompleted: (data) => {
      setCards(data.drawCards.cards);
      sessionStorage.setItem('currentReadingId', data.drawCards.id);
    }
  });

  const handleSpreadChange = (event) => {
    setSpread(event.target.value);
  };

  const handleDrawCards = async () => {
    await drawCards({
      variables: {
        spreadType: spread,
        question: question
      }
    });
  };

  return (
    <div className="tarot-reader">
      <h1>Tarot Reading</h1>
      
      <div className="spread-selector">
        <select value={spread} onChange={handleSpreadChange}>
          <option value="single">Single Card</option>
          <option value="three_card">Three Card Spread</option>
          <option value="celtic_cross">Celtic Cross</option>
        </select>
      </div>

      <div className="question-input">
        <textarea
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Enter your question..."
        />
      </div>

      <button onClick={handleDrawCards} disabled={readingLoading}>
        Draw Cards
      </button>

      <div className="cards-display">
        {cards.map((card, index) => (
          <div key={index} className="tarot-card">
            <img src={card.image} alt={card.name} />
            <h3>{card.name}</h3>
            <p>{card.isReversed ? 'Reversed' : 'Upright'}</p>
            <p>{card.isReversed ? card.meaningReversed : card.meaningUpright}</p>
          </div>
        ))}
      </div>

      {readingData && (
        <div className="reading-interpretation">
          <h2>Interpretation</h2>
          <p>{readingData.tarotReading.interpretation}</p>
        </div>
      )}
    </div>
  );
};

export default TarotReader;