.numerology-calculator {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.calculator-form {
  background: var(--card-bg);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.results-section {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.tabs button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  background: var(--button-secondary-bg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tabs button.active {
  background: var(--primary-color);
  color: white;
}

.number-display {
  text-align: center;
  margin-bottom: 2rem;
}

.number-display .number {
  font-size: 4rem;
  font-weight: bold;
  color: var(--primary-color);
  margin: 1rem 0;
}

.number-meaning {
  background: var(--card-bg-secondary);
  padding: 2rem;
  border-radius: 8px;
  margin-top: 1.5rem;
}

.meaning-content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-primary);
}

.traits-list {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.traits-list h4 {
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.traits-list ul {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  list-style: none;
  padding: 0;
}

.traits-list li {
  background: var(--tag-bg);
  color: var(--text-primary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.additional-numbers {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.number-item {
  text-align: center;
  padding: 1rem;
  background: var(--card-bg-secondary);
  border-radius: 8px;
}

.number-item label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.number-item span {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .numerology-calculator {
    padding: 1rem;
  }
  
  .calculator-form {
    padding: 1.5rem;
  }
  
  .tabs {
    flex-wrap: wrap;
  }
  
  .tabs button {
    flex: 1 1 calc(50% - 0.5rem);
  }
  
  .number-display .number {
    font-size: 3rem;
  }
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  margin-bottom: 1rem;
}

.error-message {
  background: var(--error-bg);
  color: var(--error-text);
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  text-align: center;
}

.submit-button {
  width: 100%;
  padding: 1rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-button:disabled {
  background: var(--disabled-bg);
  cursor: not-allowed;
}

.submit-button:hover:not(:disabled) {
  background: var(--primary-color-dark);
}

/* Animation classes */
.fade-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.fade-exit {
  opacity: 1;
  transform: translateY(0);
}

.fade-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}
