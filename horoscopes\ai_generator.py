import openai
from django.conf import settings
from typing import Literal, Dict, Any
from django.core.cache import cache
import asyncio
from datetime import datetime, timedelta

# Configure OpenAI API key
openai.api_key = settings.OPENAI_API_KEY

ReadingType = Literal['daily', 'weekly', 'monthly', 'yearly']

class HoroscopeGenerator:
    ELEMENTS = {
        'Fire': ['<PERSON><PERSON>', '<PERSON>', 'Sagittarius'],
        'Earth': ['Taurus', 'Virgo', 'Capricorn'],
        'Air': ['Gemini', 'Libra', 'Aquarius'],
        'Water': ['Cancer', 'Scorpio', 'Pisces']
    }

    RATE_LIMIT_KEY = "horoscope_generator_rate_limit"
    MAX_REQUESTS_PER_MINUTE = 50

    @staticmethod
    def get_element(sign: str) -> str:
        for element, signs in HoroscopeGenerator.ELEMENTS.items():
            if sign in signs:
                return element
        return ''

    @staticmethod
    def generate_prompt(sign: str, reading_type: str, birth_date: datetime = None) -> str:
        element = HoroscopeGenerator.get_element(sign)
        base_prompt = f"""Generate a {reading_type} horoscope for {sign} (a {element} sign).
        The horoscope should be positive and motivational, including:
        - General outlook
        - Love and relationships
        - Career and goals
        - Health and well-being
        Keep it concise (150-200 words) and avoid generic predictions."""

        if birth_date:
            age = (datetime.now() - birth_date).days // 365
            base_prompt += f"\nConsider that the person is approximately {age} years old."

        return base_prompt

    @staticmethod
    async def check_rate_limit() -> bool:
        current_count = cache.get(HoroscopeGenerator.RATE_LIMIT_KEY, 0)
        if current_count >= HoroscopeGenerator.MAX_REQUESTS_PER_MINUTE:
            return False
        cache.incr(HoroscopeGenerator.RATE_LIMIT_KEY)
        return True

    @staticmethod
    async def generate_horoscope(
        sign: str,
        reading_type: str = 'daily',
        birth_date: datetime = None
    ) -> Dict[str, Any]:
        if not await HoroscopeGenerator.check_rate_limit():
            raise Exception("Rate limit exceeded. Please try again later.")

        try:
            prompt = HoroscopeGenerator.generate_prompt(sign, reading_type, birth_date)
            
            response = await openai.ChatCompletion.acreate(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are an expert astrologer with deep knowledge of zodiac signs and their characteristics."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=300
            )

            prediction = response.choices[0].message.content.strip()
            
            # Generate personalized lucky numbers based on birth date if available
            if birth_date:
                lucky_number = str((birth_date.day + birth_date.month) % 99 + 1)
            else:
                lucky_number = str(random.randint(1, 99))

            # Element-based color selection
            element_colors = {
                'Fire': ['Red', 'Orange', 'Gold'],
                'Earth': ['Green', 'Brown', 'Yellow'],
                'Air': ['Blue', 'White', 'Gray'],
                'Water': ['Blue', 'Purple', 'Turquoise']
            }
            element = HoroscopeGenerator.get_element(sign)
            lucky_color = random.choice(element_colors.get(element, ['Blue', 'Green', 'Yellow']))

            return {
                'prediction': prediction,
                'lucky_number': lucky_number,
                'lucky_color': lucky_color
            }
            
        except Exception as e:
            # Log the error
            logger.error(f"Error generating horoscope: {str(e)}")
            
            # Fallback content with sign-specific generic message
            fallback_messages = {
                'Aries': "Focus on your goals with your characteristic determination.",
                'Taurus': "Stay grounded and trust your practical nature.",
                # ... [similar messages for other signs]
            }
            
            return {
                'prediction': fallback_messages.get(
                    sign,
                    f"Today's horoscope for {sign} is temporarily unavailable. Please check back later."
                ),
                'lucky_number': str(random.randint(1, 99)),
                'lucky_color': random.choice(['Blue', 'Green', 'Yellow'])
            }
