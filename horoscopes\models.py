from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class ZodiacSign(models.Model):
    name = models.CharField(max_length=50)
    date_range = models.CharField(max_length=100)
    description = models.TextField()
    element = models.CharField(max_length=20)
    qualities = models.TextField()
    symbol = models.CharField(max_length=50)
    
    def __str__(self):
        return self.name

class HoroscopeReading(models.Model):
    READING_TYPES = (
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly'),
    )
    
    zodiac_sign = models.ForeignKey(ZodiacSign, on_delete=models.CASCADE)
    reading_type = models.CharField(max_length=10, choices=READING_TYPES)
    date = models.DateField()
    prediction = models.TextField()
    lucky_number = models.CharField(max_length=20)
    lucky_color = models.Char<PERSON><PERSON>(max_length=20)
    
    class Meta:
        unique_together = ('zodiac_sign', 'reading_type', 'date')

class PersonalizedReading(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    birth_date = models.DateField()
    birth_time = models.TimeField()
    birth_place = models.CharField(max_length=200)
    generated_reading = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)