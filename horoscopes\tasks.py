import asyncio
from celery import shared_task
from django.utils import timezone
from datetime import datetime
from .models import ZodiacSign, HoroscopeReading
from .ai_generator import HoroscopeGenerator

@shared_task
async def generate_monthly_horoscopes():
    today = timezone.now().date()
    # Only generate if it's the first day of the month
    if today.day != 1:
        return
        
    generator = HoroscopeGenerator()
    
    for sign in ZodiacSign.objects.all():
        if not HoroscopeReading.objects.filter(
            zodiac_sign=sign,
            date=today,
            reading_type='monthly'
        ).exists():
            horoscope_data = await generator.generate_horoscope(sign.name, 'monthly')
            
            HoroscopeReading.objects.create(
                zodiac_sign=sign,
                reading_type='monthly',
                date=today,
                prediction=horoscope_data['prediction'],
                lucky_number=horoscope_data['lucky_number'],
                lucky_color=horoscope_data['lucky_color']
            )

@shared_task
async def generate_yearly_horoscopes():
    today = timezone.now().date()
    # Only generate if it's January 1st
    if today.month != 1 or today.day != 1:
        return
        
    generator = HoroscopeGenerator()
    
    for sign in ZodiacSign.objects.all():
        if not HoroscopeReading.objects.filter(
            zodiac_sign=sign,
            date=today,
            reading_type='yearly'
        ).exists():
            horoscope_data = await generator.generate_horoscope(sign.name, 'yearly')
            
            HoroscopeReading.objects.create(
                zodiac_sign=sign,
                reading_type='yearly',
                date=today,
                prediction=horoscope_data['prediction'],
                lucky_number=horoscope_data['lucky_number'],
                lucky_color=horoscope_data['lucky_color']
            )

@shared_task
def send_horoscope_notifications():
    """Send daily horoscope notifications to subscribed users"""
    from django.core.mail import send_mass_mail
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    today = timezone.now().date()
    
    # Get all premium users
    premium_users = User.objects.filter(is_premium=True)
    
    emails = []
    for user in premium_users:
        if user.zodiac_sign:
            reading = HoroscopeReading.objects.filter(
                zodiac_sign=user.zodiac_sign,
                date=today,
                reading_type='daily'
            ).first()
            
            if reading:
                emails.append((
                    f"Your Daily {user.zodiac_sign.name} Horoscope",
                    f"Hello {user.username},\n\n{reading.prediction}\n\n"
                    f"Lucky Number: {reading.lucky_number}\n"
                    f"Lucky Color: {reading.lucky_color}",
                    "<EMAIL>",
                    [user.email],
                ))
    
    if emails:
        send_mass_mail(emails, fail_silently=True)
