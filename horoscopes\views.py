from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from .models import ZodiacSign, HoroscopeReading, PersonalizedReading
from .serializers import ZodiacSignSerializer, HoroscopeReadingSerializer, PersonalizedReadingSerializer
from django.views.generic import ListView, DetailView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.shortcuts import render

class ZodiacSignViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = ZodiacSign.objects.all()
    serializer_class = ZodiacSignSerializer
    permission_classes = [permissions.AllowAny]

class HoroscopeReadingViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = HoroscopeReadingSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        queryset = HoroscopeReading.objects.all()
        reading_type = self.request.query_params.get('type', 'daily')
        sign = self.request.query_params.get('sign')
        
        if sign:
            queryset = queryset.filter(zodiac_sign__name=sign)
        
        return queryset.filter(
            reading_type=reading_type,
            date=timezone.now().date()
        )

    @action(detail=False, methods=['get'])
    def today(self, request):
        sign = request.query_params.get('sign')
        if not sign:
            return Response({'error': 'Sign parameter is required'}, status=400)
            
        reading = self.get_queryset().filter(zodiac_sign__name=sign).first()
        if reading:
            serializer = self.get_serializer(reading)
            return Response(serializer.data)
        return Response({'error': 'Reading not found'}, status=404)

class PersonalizedReadingViewSet(viewsets.ModelViewSet):
    serializer_class = PersonalizedReadingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return PersonalizedReading.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class HoroscopeListView(ListView):
    model = ZodiacSign
    template_name = 'horoscopes/horoscope_list.html'
    context_object_name = 'zodiac_signs'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['today'] = timezone.now().date()
        return context

class ZodiacSignDetailView(DetailView):
    model = ZodiacSign
    template_name = 'horoscopes/zodiac_sign_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        today = timezone.now().date()
        context['daily_reading'] = HoroscopeReading.objects.filter(
            zodiac_sign=self.object,
            date=today,
            reading_type='daily'
        ).first()
        return context

class PersonalizedReadingCreateView(LoginRequiredMixin, CreateView):
    model = PersonalizedReading
    template_name = 'horoscopes/personalized_reading_form.html'
    fields = ['birth_date', 'birth_time', 'birth_place']
    success_url = '/dashboard/'

    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)
