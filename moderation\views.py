from django.views.generic import TemplateView, ListView
from django.contrib.auth.mixins import UserPassesTestMixin
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from .models import ModerationAction, ModeratedItem
from blog.models import Comment

class ModeratorRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_staff or self.request.user.groups.filter(name='moderators').exists()

class ModerationDashboardView(ModeratorRequiredMixin, TemplateView):
    template_name = 'moderation/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Time ranges for analytics
        now = timezone.now()
        last_24h = now - timedelta(hours=24)
        last_week = now - timedelta(days=7)
        last_month = now - timedelta(days=30)

        # Comment statistics
        context['stats'] = {
            'pending_reviews': ModeratedItem.objects.filter(status='pending').count(),
            'reported_today': ModeratedItem.objects.filter(
                created_at__gte=last_24h
            ).count(),
            'spam_detected': ModeratedItem.objects.filter(status='spam').count(),
        }

        # Moderation activity
        context['recent_actions'] = ModerationAction.objects.select_related(
            'moderator'
        ).prefetch_related('content_object')[:10]

        # Trending issues
        context['trending_issues'] = ModeratedItem.objects.filter(
            created_at__gte=last_week
        ).values('content_type__model').annotate(
            count=Count('id')
        ).order_by('-count')[:5]

        return context

class ModeratedItemListView(ModeratorRequiredMixin, ListView):
    model = ModeratedItem
    template_name = 'moderation/item_list.html'
    context_object_name = 'items'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        return queryset.select_related('content_type', 'last_moderator')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_filters'] = ModeratedItem.STATUS_CHOICES
        return context