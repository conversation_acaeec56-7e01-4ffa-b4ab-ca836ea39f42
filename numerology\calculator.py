from datetime import datetime
from typing import Dict, Any

class NumerologyCalculator:
    @staticmethod
    def reduce_to_single_digit(number: int) -> int:
        """Reduce a number to a single digit (except 11, 22, 33)"""
        if number in (11, 22, 33):
            return number
        
        while number > 9:
            number = sum(int(digit) for digit in str(number))
        return number

    @staticmethod
    def calculate_life_path(birth_date: datetime) -> int:
        """Calculate Life Path Number from birth date"""
        date_str = birth_date.strftime('%Y%m%d')
        number = sum(int(digit) for digit in date_str)
        return NumerologyCalculator.reduce_to_single_digit(number)

    @staticmethod
    def calculate_destiny(full_name: str) -> int:
        """Calculate Destiny Number from full name"""
        letter_values = {
            'A': 1, 'J': 1, 'S': 1,
            'B': 2, 'K': 2, 'T': 2,
            'C': 3, 'L': 3, 'U': 3,
            'D': 4, 'M': 4, 'V': 4,
            'E': 5, 'N': 5, 'W': 5,
            'F': 6, 'O': 6, 'X': 6,
            'G': 7, 'P': 7, 'Y': 7,
            'H': 8, 'Q': 8, 'Z': 8,
            'I': 9, 'R': 9
        }
        
        name_sum = sum(letter_values.get(char.upper(), 0) for char in full_name if char.isalpha())
        return NumerologyCalculator.reduce_to_single_digit(name_sum)

    @staticmethod
    def calculate_soul_urge(full_name: str) -> int:
        """Calculate Soul Urge Number from vowels in name"""
        vowels = 'AEIOU'
        vowel_values = {'A': 1, 'E': 5, 'I': 9, 'O': 6, 'U': 3}
        
        vowel_sum = sum(vowel_values[char.upper()] 
                       for char in full_name 
                       if char.upper() in vowels)
        return NumerologyCalculator.reduce_to_single_digit(vowel_sum)

    @staticmethod
    def calculate_personality(full_name: str) -> int:
        """Calculate Personality Number from consonants in name"""
        consonant_values = {
            'B': 2, 'C': 3, 'D': 4, 'F': 6, 'G': 7, 'H': 8, 'J': 1,
            'K': 2, 'L': 3, 'M': 4, 'N': 5, 'P': 7, 'Q': 8, 'R': 9,
            'S': 1, 'T': 2, 'V': 4, 'W': 5, 'X': 6, 'Y': 7, 'Z': 8
        }
        
        consonant_sum = sum(consonant_values[char.upper()] 
                          for char in full_name 
                          if char.upper() in consonant_values)
        return NumerologyCalculator.reduce_to_single_digit(consonant_sum)

    @staticmethod
    def calculate_full_profile(birth_date: datetime, full_name: str) -> Dict[str, Any]:
        """Calculate complete numerology profile"""
        return {
            'life_path_number': NumerologyCalculator.calculate_life_path(birth_date),
            'destiny_number': NumerologyCalculator.calculate_destiny(full_name),
            'soul_urge_number': NumerologyCalculator.calculate_soul_urge(full_name),
            'personality_number': NumerologyCalculator.calculate_personality(full_name),
            'birth_day_number': NumerologyCalculator.reduce_to_single_digit(birth_date.day),
            'current_year_number': NumerologyCalculator.reduce_to_single_digit(datetime.now().year)
        }