from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class NumerologyReading(models.Model):
    READING_TYPES = (
        ('life_path', 'Life Path Number'),
        ('destiny', 'Destiny Number'),
        ('soul_urge', 'Soul Urge Number'),
        ('personality', 'Personality Number'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    reading_type = models.CharField(max_length=20, choices=READING_TYPES)
    number = models.IntegerField()
    interpretation = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

class NumerologyProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    life_path_number = models.IntegerField()
    destiny_number = models.IntegerField()
    soul_urge_number = models.IntegerField()
    personality_number = models.IntegerField()
    birth_day_number = models.IntegerField()
    current_year_number = models.IntegerField()
    
    updated_at = models.DateTimeField(auto_now=True)