import graphene
from graphene_django import DjangoObjectType
from .models import NumerologyReading, NumerologyProfile
from .calculator import NumerologyCalculator
from datetime import datetime

class NumerologyReadingType(DjangoObjectType):
    class Meta:
        model = NumerologyReading

class NumerologyProfileType(DjangoObjectType):
    class Meta:
        model = NumerologyProfile

class CalculateNumerologyInput(graphene.InputObjectType):
    full_name = graphene.String(required=True)
    birth_date = graphene.Date(required=True)

class CalculateNumerologyPayload(graphene.ObjectType):
    life_path_number = graphene.Int()
    destiny_number = graphene.Int()
    soul_urge_number = graphene.Int()
    personality_number = graphene.Int()
    birth_day_number = graphene.Int()
    current_year_number = graphene.Int()
    reading = graphene.Field(NumerologyReadingType)

class NumerologyMutation(graphene.Mutation):
    class Arguments:
        input = CalculateNumerologyInput(required=True)

    Output = CalculateNumerologyPayload

    def mutate(self, info, input):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")

        calculator = NumerologyCalculator()
        profile_data = calculator.calculate_full_profile(
            input.birth_date,
            input.full_name
        )

        # Update or create user's numerology profile
        profile, _ = NumerologyProfile.objects.update_or_create(
            user=user,
            defaults=profile_data
        )

        # Create a new reading
        reading = NumerologyReading.objects.create(
            user=user,
            reading_type='life_path',
            number=profile_data['life_path_number'],
            interpretation=get_number_interpretation('life_path', profile_data['life_path_number'])
        )

        return CalculateNumerologyPayload(
            reading=reading,
            **profile_data
        )

class Query(graphene.ObjectType):
    numerology_profile = graphene.Field(NumerologyProfileType)
    numerology_readings = graphene.List(NumerologyReadingType)

    def resolve_numerology_profile(self, info):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
        return NumerologyProfile.objects.filter(user=user).first()

    def resolve_numerology_readings(self, info):
        user = info.context.user
        if not user.is_authenticated:
            raise Exception("Authentication required")
        return NumerologyReading.objects.filter(user=user)

class Mutation(graphene.ObjectType):
    calculate_numerology = NumerologyMutation.Field()