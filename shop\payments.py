import stripe
from django.conf import settings
from django.core.mail import send_mail
from typing import Dict, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)
stripe.api_key = settings.STRIPE_SECRET_KEY

class StripePaymentHandler:
    @staticmethod
    def create_checkout_session(user, success_url: str, cancel_url: str) -> Dict[str, Any]:
        try:
            # Create or get Stripe customer
            if not user.stripe_customer_id:
                customer = stripe.Customer.create(
                    email=user.email,
                    metadata={'user_id': user.id}
                )
                user.stripe_customer_id = customer.id
                user.save()

            checkout_session = stripe.checkout.Session.create(
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                line_items=[{
                    'price': settings.STRIPE_PREMIUM_PRICE_ID,
                    'quantity': 1,
                }],
                mode='subscription',
                success_url=success_url,
                cancel_url=cancel_url,
                metadata={
                    'user_id': user.id
                }
            )
            return {'session_id': checkout_session.id}
        except Exception as e:
            logger.error(f"Stripe checkout session creation failed: {str(e)}")
            return {'error': str(e)}

    @staticmethod
    def handle_webhook_event(payload: str, sig_header: str) -> bool:
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )

            if event.type == 'checkout.session.completed':
                session = event.data.object
                user_id = session.metadata.get('user_id')
                
                from django.contrib.auth import get_user_model
                User = get_user_model()
                
                user = User.objects.get(id=user_id)
                user.is_premium = True
                user.premium_until = datetime.now() + timedelta(days=30)
                user.save()

                # Send welcome email
                send_mail(
                    'Welcome to Premium!',
                    'Thank you for subscribing to our premium service.',
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    fail_silently=True,
                )

            elif event.type == 'customer.subscription.deleted':
                subscription = event.data.object
                user_id = subscription.metadata.get('user_id')
                
                from django.contrib.auth import get_user_model
                User = get_user_model()
                
                user = User.objects.get(id=user_id)
                user.is_premium = False
                user.save()

            return True

        except Exception as e:
            logger.error(f"Webhook handling failed: {str(e)}")
            return False

    @staticmethod
    def cancel_subscription(user) -> Dict[str, Any]:
        try:
            subscription = stripe.Subscription.list(
                customer=user.stripe_customer_id,
                limit=1
            ).data[0]

            stripe.Subscription.delete(subscription.id)

            user.is_premium = False
            user.save()

            return {'success': True}
        except Exception as e:
            logger.error(f"Subscription cancellation failed: {str(e)}")
            return {'error': str(e)}
