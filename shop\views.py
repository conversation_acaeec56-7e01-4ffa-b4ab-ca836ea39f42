from django.views.generic import TemplateView, ListView, DetailView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.urls import reverse
from django.conf import settings
from .payments import StripePaymentHandler
from .models import Product, Category

class PremiumSubscriptionView(LoginRequiredMixin, TemplateView):
    template_name = 'shop/premium_subscription.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'stripe_public_key': settings.STRIPE_PUBLIC_KEY,
            'premium_price': settings.PREMIUM_SUBSCRIPTION_PRICE
        })
        return context

    def post(self, request, *args, **kwargs):
        success_url = request.build_absolute_uri(reverse('premium_success'))
        cancel_url = request.build_absolute_uri(reverse('premium_subscription'))
        
        result = StripePaymentHandler.create_checkout_session(
            request.user, success_url, cancel_url
        )
        
        return JsonResponse(result)

@csrf_exempt
def stripe_webhook(request):
    if request.method == 'POST':
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        
        success = StripePaymentHandler.handle_webhook_event(payload, sig_header)
        
        if success:
            return HttpResponse(status=200)
    return HttpResponse(status=400)

class PremiumSuccessView(LoginRequiredMixin, TemplateView):
    template_name = 'shop/premium_success.html'

class ProductListView(ListView):
    model = Product
    template_name = 'shop/product_list.html'
    context_object_name = 'products'
    paginate_by = 12

    def get_queryset(self):
        queryset = Product.objects.all()
        category_slug = self.request.GET.get('category')
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        return context

class ProductDetailView(DetailView):
    model = Product
    template_name = 'shop/product_detail.html'
    context_object_name = 'product'
