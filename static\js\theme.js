// Theme toggle functionality
const themeToggle = document.getElementById('theme-toggle');
const htmlElement = document.documentElement;

// Check for saved theme preference
const savedTheme = localStorage.getItem('theme') || 'light';
htmlElement.setAttribute('data-theme', savedTheme);

// Update toggle button state
themeToggle.checked = savedTheme === 'dark';

// Theme toggle handler
function toggleTheme(e) {
    const isDark = e.target.checked;
    const theme = isDark ? 'dark' : 'light';
    htmlElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
}

themeToggle.addEventListener('change', toggleTheme);