from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class TarotCard(models.Model):
    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to='tarot_cards/')
    meaning_upright = models.TextField()
    meaning_reversed = models.TextField()
    description = models.TextField()
    keywords = models.CharField(max_length=200)
    
    def __str__(self):
        return self.name

class TarotReading(models.Model):
    SPREAD_TYPES = (
        ('single', 'Single Card'),
        ('three_card', 'Three Card Spread'),
        ('celtic_cross', 'Celtic Cross'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    spread_type = models.CharField(max_length=20, choices=SPREAD_TYPES)
    cards = models.ManyToManyField(TarotCard, through='TarotReadingCard')
    question = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    interpretation = models.TextField()

class TarotReadingCard(models.Model):
    reading = models.ForeignKey(TarotReading, on_delete=models.CASCADE)
    card = models.ForeignKey(TarotCard, on_delete=models.CASCADE)
    position = models.IntegerField()
    is_reversed = models.BooleanField(default=False)
    position_meaning = models.CharField(max_length=100)

class NumerologyReading(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    birth_date = models.DateField()
    full_name = models.CharField(max_length=200)
    life_path_number = models.IntegerField()
    destiny_number = models.IntegerField()
    soul_urge_number = models.IntegerField()
    personality_number = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    interpretation = models.TextField()