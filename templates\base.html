<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Astrology Project{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'css/theme.css' %}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'home' %}">Astrology</a>
            <div class="navbar-nav">
                <a class="nav-link" href="{% url 'horoscope_list' %}">Horoscopes</a>
                <a class="nav-link" href="{% url 'shop:product_list' %}">Shop</a>
                <a class="nav-link" href="{% url 'forum:topic_list' %}">Forum</a>
                {% if user.is_authenticated %}
                    <a class="nav-link" href="{% url 'dashboard' %}">Dashboard</a>
                    <a class="nav-link" href="{% url 'account_logout' %}">Logout</a>
                {% else %}
                    <a class="nav-link" href="{% url 'account_login' %}">Login</a>
                {% endif %}
            </div>
            <div class="theme-toggle">
                <input type="checkbox" id="theme-toggle" class="theme-toggle-input">
                <label for="theme-toggle" class="theme-toggle-label">
                    <i class="fas fa-sun"></i>
                    <i class="fas fa-moon"></i>
                </label>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
        {% block content %}{% endblock %}
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{% static 'js/theme.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
