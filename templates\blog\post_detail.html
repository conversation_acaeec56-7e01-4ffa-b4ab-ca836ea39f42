{% extends 'blog/base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/blog.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="container">
    <article class="blog-post">
        {% if post.featured_image %}
        <img src="{{ post.featured_image.url }}" class="img-fluid rounded mb-4" alt="{{ post.title }}">
        {% endif %}
        
        <h1 class="mb-4">{{ post.title }}</h1>
        
        <div class="post-meta text-muted mb-4">
            <span><i class="fas fa-user"></i> {{ post.author.get_full_name }}</span>
            <span class="mx-2">|</span>
            <span><i class="fas fa-calendar"></i> {{ post.published_at|date }}</span>
            <span class="mx-2">|</span>
            <span><i class="fas fa-folder"></i> {{ post.category.name }}</span>
        </div>

        <div class="post-content">
            {{ post.content|safe }}
        </div>

        {% if post.tags.exists %}
        <div class="post-tags mt-4">
            {% for tag in post.tags.all %}
            <a href="{% url 'blog:tag_posts' tag.slug %}" class="badge bg-secondary text-decoration-none">
                #{{ tag.name }}
            </a>
            {% endfor %}
        </div>
        {% endif %}

        {% include 'blog/includes/social_share.html' %}
    </article>
</div>

<section class="comments-section mt-5">
    <h3>Comments ({{ post.comments.count }})</h3>
    
    {% if user.is_authenticated %}
        <div class="comment-form mb-4">
            <form method="post">
                {% csrf_token %}
                {{ comment_form.non_field_errors }}
                {{ comment_form.parent_id }}
                <div class="form-group">
                    {{ comment_form.content }}
                </div>
                <button type="submit" class="btn btn-primary mt-2">Post Comment</button>
            </form>
        </div>
    {% else %}
        <p>Please <a href="{% url 'login' %}?next={{ request.path }}">login</a> to leave a comment.</p>
    {% endif %}

    <div class="comments-list">
        {% for comment in comments %}
            <div class="comment mb-4" id="comment-{{ comment.id }}">
                <div class="card">
                    <div class="card-body">
                        <div class="comment-meta mb-2">
                            <strong>{{ comment.author.get_full_name|default:comment.author.username }}</strong>
                            <small class="text-muted">{{ comment.created_at|timesince }} ago</small>
                        </div>
                        <div class="comment-content">
                            {{ comment.content|linebreaks }}
                        </div>
                        <div class="comment-actions">
                            {% if user.is_authenticated %}
                                <button class="btn btn-sm btn-link reply-btn" 
                                        data-comment-id="{{ comment.id }}"
                                        onclick="showReplyForm({{ comment.id }})">
                                    Reply
                                </button>
                                
                                {% if user != comment.author %}
                                    <button class="btn btn-sm btn-link text-danger report-btn"
                                            data-comment-id="{{ comment.id }}"
                                            onclick="reportComment({{ comment.id }})">
                                        Report
                                    </button>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Replies -->
                <div class="replies ml-4 mt-2">
                    {% for reply in comment.replies.all %}
                        <div class="comment reply mb-2" id="comment-{{ reply.id }}">
                            <div class="card">
                                <div class="card-body">
                                    <div class="comment-meta mb-2">
                                        <strong>{{ reply.author.get_full_name|default:reply.author.username }}</strong>
                                        <small class="text-muted">{{ reply.created_at|timesince }} ago</small>
                                    </div>
                                    <div class="comment-content">
                                        {{ reply.content|linebreaks }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% empty %}
            <p>No comments yet. Be the first to comment!</p>
        {% endfor %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
function showReplyForm(commentId) {
    const commentForm = document.querySelector('.comment-form').cloneNode(true);
    const oldReplyForm = document.querySelector('.reply-form');
    if (oldReplyForm) {
        oldReplyForm.remove();
    }
    
    commentForm.classList.add('reply-form');
    commentForm.classList.add('ml-4');
    commentForm.querySelector('[name="parent_id"]').value = commentId;
    
    const commentElement = document.querySelector(`#comment-${commentId}`);
    const repliesSection = commentElement.querySelector('.replies');
    repliesSection.insertBefore(commentForm, repliesSection.firstChild);
}

async function reportComment(commentId) {
    if (!confirm('Are you sure you want to report this comment?')) {
        return;
    }

    try {
        const response = await fetch(`/blog/comment/${commentId}/report/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        });

        if (response.ok) {
            const data = await response.json();
            alert(data.message);
            if (data.hidden) {
                document.querySelector(`#comment-${commentId}`).style.opacity = '0.5';
            }
        } else {
            throw new Error('Failed to report comment');
        }
    } catch (error) {
        alert('Error reporting comment. Please try again later.');
    }
}
</script>
{% endblock %}
