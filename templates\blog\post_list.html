{% extends 'base.html' %}

{% block title %}Blog - Astrological Insights{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Search Bar -->
            <form class="mb-4" method="get">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Search posts..." 
                           value="{{ request.GET.q }}">
                    <button class="btn btn-primary" type="submit">Search</button>
                </div>
            </form>

            <!-- Featured Posts -->
            {% if not request.GET.q and not request.GET.category %}
                {% if featured_posts %}
                <div class="mb-4">
                    <h2>Featured Posts</h2>
                    <div class="row">
                        {% for post in featured_posts %}
                        <div class="col-md-4">
                            <div class="card h-100">
                                {% if post.featured_image %}
                                <img src="{{ post.featured_image.url }}" class="card-img-top" alt="{{ post.title }}">
                                {% endif %}
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{{ post.get_absolute_url }}" class="text-decoration-none">
                                            {{ post.title }}
                                        </a>
                                    </h5>
                                    <p class="card-text">{{ post.excerpt }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            {% endif %}

            <!-- Post List -->
            <h2>Latest Posts</h2>
            {% for post in posts %}
            <div class="card mb-4">
                {% if post.featured_image %}
                <img src="{{ post.featured_image.url }}" class="card-img-top" alt="{{ post.title }}">
                {% endif %}
                <div class="card-body">
                    <h3 class="card-title">
                        <a href="{{ post.get_absolute_url }}" class="text-decoration-none">
                            {{ post.title }}
                        </a>
                    </h3>
                    <p class="text-muted">
                        By {{ post.author.username }} | 
                        {{ post.published_at|date }} |
                        {{ post.views_count }} views
                    </p>
                    <p class="card-text">{{ post.excerpt }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group">
                            <a href="{{ post.get_absolute_url }}" class="btn btn-sm btn-outline-primary">
                                Read More
                            </a>
                        </div>
                        <small class="text-muted">
                            {{ post.comments.count }} comments
                        </small>
                    </div>
                </div>
            </div>
            {% empty %}
            <p>No posts found.</p>
            {% endfor %}

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Categories</h4>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        {% for category in categories %}
                        <li>
                            <a href="?category={{ category.slug }}" class="text-decoration-none">
                                {{ category.name }}
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}