{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container py-5">
    <h1 class="text-center mb-5">Zodiac Compatibility Checker</h1>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form id="compatibility-form" class="row g-3">
                        <div class="col-md-6">
                            <label for="sign1" class="form-label">Select First Sign</label>
                            <select class="form-select" id="sign1" name="sign1" required>
                                <option value="">Choose...</option>
                                {% for sign in zodiac_signs %}
                                <option value="{{ sign.name }}">{{ sign.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="sign2" class="form-label">Select Second Sign</label>
                            <select class="form-select" id="sign2" name="sign2" required>
                                <option value="">Choose...</option>
                                {% for sign in zodiac_signs %}
                                <option value="{{ sign.name }}">{{ sign.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-12 text-center">
                            <button type="submit" class="btn btn-primary">Check Compatibility</button>
                        </div>
                    </form>
                </div>
            </div>

            {% if compatibility %}
            <div class="card mt-4">
                <div class="card-body">
                    <h3 class="card-title text-center">Compatibility Results</h3>
                    <div class="compatibility-scores">
                        <div class="score-item">
                            <h4>Love Compatibility</h4>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: {{ compatibility.love_score }}%">
                                    {{ compatibility.love_score }}%
                                </div>
                            </div>
                        </div>
                        <div class="score-item mt-3">
                            <h4>Friendship Compatibility</h4>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: {{ compatibility.friendship_score }}%">
                                    {{ compatibility.friendship_score }}%
                                </div>
                            </div>
                        </div>
                        <div class="score-item mt-3">
                            <h4>Work Compatibility</h4>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: {{ compatibility.work_score }}%">
                                    {{ compatibility.work_score }}%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="compatibility-description mt-4">
                        {{ compatibility.description|linebreaks }}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('compatibility-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    window.location.href = `?sign1=${formData.get('sign1')}&sign2=${formData.get('sign2')}`;
});
</script>
{% endblock %}