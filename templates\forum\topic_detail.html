{% extends 'base.html' %}

{% block title %}{{ topic.title }}{% endblock %}

{% block content %}
<div class="container">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'forum:category_list' %}">Forum</a></li>
            <li class="breadcrumb-item"><a href="{{ topic.category.get_absolute_url }}">{{ topic.category.name }}</a></li>
            <li class="breadcrumb-item active">{{ topic.title }}</li>
        </ol>
    </nav>

    <div class="card mb-4">
        <div class="card-header">
            <h2 class="mb-0">{{ topic.title }}</h2>
            <small class="text-muted">
                Posted by {{ topic.author.username }} on {{ topic.created_at|date }}
            </small>
        </div>
        <div class="card-body">
            {{ topic.content|linebreaks }}
        </div>
    </div>

    <h3 class="mb-3">Replies</h3>
    {% for reply in replies %}
    <div class="card mb-3 {% if reply.is_solution %}border-success{% endif %}">
        <div class="card-body">
            {{ reply.content|linebreaks }}
            <div class="text-muted">
                <small>
                    Reply by {{ reply.author.username }} on {{ reply.created_at|date }}
                    {% if reply.is_solution %}
                    <span class="badge bg-success">Solution</span>
                    {% endif %}
                </small>
            </div>
        </div>
    </div>
    {% empty %}
    <p>No replies yet.</p>
    {% endfor %}

    {% if user.is_authenticated and not topic.is_locked %}
    <div class="card mt-4">
        <div class="card-header">
            <h4 class="mb-0">Post a Reply</h4>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'forum:reply_create' topic.category.slug topic.slug %}">
                {% csrf_token %}
                {{ reply_form.as_p }}
                <button type="submit" class="btn btn-primary">Submit Reply</button>
            </form>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}