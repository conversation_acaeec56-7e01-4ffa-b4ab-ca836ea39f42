{% extends 'base.html' %}

{% block title %}{{ object.name }} Horoscope{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h1>{{ object.name }}</h1>
        <p class="lead">{{ object.date_range }}</p>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Today's Horoscope</h5>
                {% if daily_reading %}
                    <p>{{ daily_reading.prediction }}</p>
                    <div class="mt-3">
                        <p><strong>Lucky Number:</strong> {{ daily_reading.lucky_number }}</p>
                        <p><strong>Lucky Color:</strong> {{ daily_reading.lucky_color }}</p>
                    </div>
                {% else %}
                    <p>Today's horoscope is not available yet.</p>
                {% endif %}
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">About {{ object.name }}</h5>
                <p><strong>Element:</strong> {{ object.element }}</p>
                <p><strong>Symbol:</strong> {{ object.symbol }}</p>
                <p><strong>Qualities:</strong> {{ object.qualities }}</p>
                <p>{{ object.description }}</p>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        {% if user.is_authenticated %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Get a Personalized Reading</h5>
                    <p>Get a detailed personal reading based on your birth details.</p>
                    <a href="{% url 'personalized_reading_create' %}" class="btn btn-primary">Get Reading</a>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}