{% extends 'base.html' %}
{% load static %}

{% block title %}Moderation Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'moderation/css/dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-2 d-none d-md-block bg-light sidebar">
            <div class="sidebar-sticky">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="{% url 'moderation:dashboard' %}">
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'moderation:items' %}?status=pending">
                            Pending Reviews
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'moderation:items' %}?status=spam">
                            Spam Queue
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main role="main" class="col-md-10 ml-sm-auto px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1>Moderation Dashboard</h1>
            </div>

            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Pending Reviews</h5>
                            <p class="card-text display-4">{{ stats.pending_reviews }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-warning mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Reported Today</h5>
                            <p class="card-text display-4">{{ stats.reported_today }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-white bg-danger mb-3">
                        <div class="card-body">
                            <h5 class="card-title">Spam Detected</h5>
                            <p class="card-text display-4">{{ stats.spam_detected }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <h2>Recent Moderation Actions</h2>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Moderator</th>
                                    <th>Action</th>
                                    <th>Item</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for action in recent_actions %}
                                <tr>
                                    <td>{{ action.timestamp|timesince }} ago</td>
                                    <td>{{ action.moderator.username }}</td>
                                    <td>{{ action.get_action_type_display }}</td>
                                    <td>{{ action.content_object }}</td>
                                    <td>{{ action.notes|truncatechars:50 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Trending Issues -->
            <div class="row mt-4">
                <div class="col-12">
                    <h2>Trending Issues</h2>
                    <div class="list-group">
                        {% for issue in trending_issues %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            {{ issue.content_type__model|title }}
                            <span class="badge badge-primary badge-pill">{{ issue.count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'moderation/js/dashboard.js' %}"></script>
{% endblock %}