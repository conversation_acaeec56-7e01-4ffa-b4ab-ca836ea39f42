{% extends 'base.html' %}

{% block title %}Moderation Queue{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar (same as dashboard) -->
        <nav class="col-md-2 d-none d-md-block bg-light sidebar">
            <!-- ... -->
        </nav>

        <!-- Main content -->
        <main role="main" class="col-md-10 ml-sm-auto px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1>Moderation Queue</h1>
            </div>

            <!-- Filters -->
            <div class="mb-3">
                <div class="btn-group">
                    {% for status_code, status_name in status_filters %}
                    <a href="?status={{ status_code }}" 
                       class="btn btn-outline-primary {% if request.GET.status == status_code %}active{% endif %}">
                        {{ status_name }}
                    </a>
                    {% endfor %}
                </div>
            </div>

            <!-- Items List -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Content</th>
                            <th>Reports</th>
                            <th>Created</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in items %}
                        <tr>
                            <td>{{ item.content_type.model|title }}</td>
                            <td>{{ item.content_object|truncatechars:100 }}</td>
                            <td>{{ item.reports_count }}</td>
                            <td>{{ item.created_at|timesince }} ago</td>
                            <td>
                                <span class="badge badge-{{ item.status|lower }}">
                                    {{ item.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button class="btn btn-sm btn-success" 
                                            onclick="moderateItem({{ item.id }}, 'approve')">
                                        Approve
                                    </button>
                                    <button class="btn btn-sm btn-danger" 
                                            onclick="moderateItem({{ item.id }}, 'reject')">
                                        Reject
                                    </button>
                                    <button class="btn btn-sm btn-warning" 
                                            onclick="moderateItem({{ item.id }}, 'spam')">
                                        Spam
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </main>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
async function moderateItem(itemId, action) {
    try {
        const response = await fetch(`/moderation/item/${itemId}/${action}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            },
        });

        if (response.ok) {
            location.reload();
        } else {
            throw new Error('Moderation action failed');
        }
    } catch (error) {
        alert('Error performing moderation action. Please try again.');
    }
}
</script>
{% endblock %}