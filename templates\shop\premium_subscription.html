{% extends 'base.html' %}
{% block title %}Premium Subscription{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h2 class="mb-0">Premium Membership</h2>
                </div>
                <div class="card-body">
                    <h3 class="text-center mb-4">${{ premium_price }}/month</h3>
                    
                    <div class="features-list mb-4">
                        <h4>Premium Features:</h4>
                        <ul class="list-group">
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i>
                                Daily Personalized Horoscope Emails
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i>
                                Detailed Weekly Analysis
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i>
                                Exclusive Astrological Insights
                            </li>
                            <li class="list-group-item">
                                <i class="fas fa-check text-success"></i>
                                Priority Customer Support
                            </li>
                        </ul>
                    </div>

                    <button id="checkout-button" class="btn btn-primary btn-lg btn-block">
                        Subscribe Now
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://js.stripe.com/v3/"></script>
<script>
    const stripe = Stripe('{{ stripe_public_key }}');
    const checkoutButton = document.getElementById('checkout-button');

    checkoutButton.addEventListener('click', function() {
        fetch('{% url "premium_subscription" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
            }
        })
        .then(response => response.json())
        .then(session => {
            if (session.error) {
                alert('Error: ' + session.error);
            } else {
                return stripe.redirectToCheckout({ sessionId: session.session_id });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    });
</script>
{% endblock %}