{% extends 'base.html' %}

{% block title %}{{ product.name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-6">
            {% if product.image %}
            <img src="{{ product.image.url }}" class="img-fluid rounded" alt="{{ product.name }}">
            {% endif %}
        </div>
        <div class="col-md-6">
            <h1 class="mb-3">{{ product.name }}</h1>
            <p class="text-muted">Category: {{ product.category.name }}</p>
            <h2 class="text-primary mb-3">${{ product.price }}</h2>
            <div class="mb-4">
                {{ product.description|linebreaks }}
            </div>
            {% if product.stock > 0 %}
            <p class="text-success">In Stock ({{ product.stock }} available)</p>
            <button class="btn btn-primary btn-lg">Add to Cart</button>
            {% else %}
            <p class="text-danger">Out of Stock</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}