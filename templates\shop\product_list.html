{% extends 'base.html' %}

{% block title %}Shop - Products{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <!-- Sidebar with categories -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Categories</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'shop:product_list' %}" 
                       class="list-group-item {% if not request.GET.category %}active{% endif %}">
                        All Products
                    </a>
                    {% for category in categories %}
                    <a href="?category={{ category.slug }}" 
                       class="list-group-item {% if request.GET.category == category.slug %}active{% endif %}">
                        {{ category.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Product grid -->
        <div class="col-md-9">
            <div class="row row-cols-1 row-cols-md-3 g-4">
                {% for product in products %}
                <div class="col">
                    <div class="card h-100">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" class="card-img-top" alt="{{ product.name }}">
                        {% endif %}
                        <div class="card-body">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text">{{ product.description|truncatewords:20 }}</p>
                            <p class="card-text"><strong>${{ product.price }}</strong></p>
                            <a href="{% url 'shop:product_detail' product.slug %}" class="btn btn-primary">View Details</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    <li class="page-item {% if page_obj.number == num %}active{% endif %}">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}