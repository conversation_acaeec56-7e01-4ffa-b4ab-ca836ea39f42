{% extends 'base.html' %}

{% block title %}My Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">Profile Information</h5>
                <p><strong>Username:</strong> {{ user.username }}</p>
                <p><strong>Email:</strong> {{ user.email }}</p>
                <p><strong>Zodiac Sign:</strong> {{ user.zodiac_sign.name }}</p>
                <p><strong>Member Since:</strong> {{ user.date_joined|date }}</p>
                <a href="{% url 'account_change_password' %}" class="btn btn-secondary">Change Password</a>
            </div>
        </div>

        {% if user.is_premium %}
            <div class="card mb-4 bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Premium Member</h5>
                    <p>Enjoy your exclusive benefits!</p>
                </div>
            </div>
        {% else %}
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">Upgrade to Premium</h5>
                    <p>Get access to exclusive features and detailed readings!</p>
                    <a href="{% url 'shop:premium_subscription' %}" class="btn btn-primary">Upgrade Now</a>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="col-md-8">
        <h2>Recent Readings</h2>
        {% if personalized_readings %}
            <div class="list-group mb-4">
                {% for reading in personalized_readings %}
                    <div class="list-group-item">
                        <h6 class="mb-1">Reading from {{ reading.created_at|date }}</h6>
                        <p class="mb-1">{{ reading.generated_reading|truncatewords:50 }}</p>
                        <small>Birth Date: {{ reading.birth_date }}</small>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p>No personalized readings yet.</p>
        {% endif %}

        <h2>Recent Orders</h2>
        {% if recent_orders %}
            <div class="list-group">
                {% for order in recent_orders %}
                    <div class="list-group-item">
                        <h6 class="mb-1">Order #{{ order.id }}</h6>
                        <p class="mb-1">Status: {{ order.get_status_display }}</p>
                        <small>Total: ${{ order.total_amount }}</small>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p>No orders yet.</p>
        {% endif %}
    </div>
</div>
{% endblock %}