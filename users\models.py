from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from datetime import datetime

class CustomUser(AbstractUser):
    birth_date = models.DateField(null=True, blank=True)
    zodiac_sign = models.ForeignKey('horoscopes.ZodiacSign', on_delete=models.SET_NULL, null=True)
    is_premium = models.BooleanField(default=False)
    premium_until = models.DateTimeField(null=True, blank=True)
    stripe_customer_id = models.CharField(max_length=100, blank=True, null=True)
    notification_preferences = models.JSONField(default=dict)
    profile_picture = models.ImageField(upload_to='profile_pics/', null=True, blank=True)
    bio = models.TextField(max_length=500, blank=True)
    
    # Additional fields for horoscope preferences
    preferred_reading_time = models.TimeField(default=timezone.now)
    preferred_reading_types = models.JSONField(default=list)
    last_reading_date = models.DateField(null=True, blank=True)
    
    class Meta:
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def save(self, *args, **kwargs):
        # Automatically determine zodiac sign if birth_date is provided
        if self.birth_date and not self.zodiac_sign:
            self.zodiac_sign = self.determine_zodiac_sign()
        super().save(*args, **kwargs)

    def determine_zodiac_sign(self):
        if not self.birth_date:
            return None
            
        from horoscopes.models import ZodiacSign
        
        month = self.birth_date.month
        day = self.birth_date.day
        
        zodiac_dates = {
            'Aries': ((3, 21), (4, 19)),
            'Taurus': ((4, 20), (5, 20)),
            'Gemini': ((5, 21), (6, 20)),
            'Cancer': ((6, 21), (7, 22)),
            'Leo': ((7, 23), (8, 22)),
            'Virgo': ((8, 23), (9, 22)),
            'Libra': ((9, 23), (10, 22)),
            'Scorpio': ((10, 23), (11, 21)),
            'Sagittarius': ((11, 22), (12, 21)),
            'Capricorn': ((12, 22), (1, 19)),
            'Aquarius': ((1, 20), (2, 18)),
            'Pisces': ((2, 19), (3, 20))
        }
        
        for sign, ((start_month, start_day), (end_month, end_day)) in zodiac_dates.items():
            if (month == start_month and day >= start_day) or (month == end_month and day <= end_day):
                return ZodiacSign.objects.get(name=sign)
        
        # Handle Capricorn edge case
        if month == 12 and day >= 22 or month == 1 and day <= 19:
            return ZodiacSign.objects.get(name='Capricorn')
            
        return None

    def check_premium_status(self):
        if self.premium_until and self.premium_until < timezone.now():
            self.is_premium = False
            self.save()
        return self.is_premium

    def get_reading_preferences(self):
        return {
            'preferred_time': self.preferred_reading_time,
            'reading_types': self.preferred_reading_types,
            'zodiac_sign': self.zodiac_sign.name if self.zodiac_sign else None
        }

    def update_notification_preferences(self, preferences):
        self.notification_preferences.update(preferences)
        self.save()
