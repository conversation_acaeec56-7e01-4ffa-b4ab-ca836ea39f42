from django.conf import settings
from django_otp import devices_for_user
from django_otp.plugins.otp_totp.models import TOTPDevice
from django_otp.plugins.otp_static.models import StaticDevice

def get_user_totp_device(user, confirmed=None):
    devices = devices_for_user(user, confirmed=confirmed)
    for device in devices:
        if isinstance(device, TOTPDevice):
            return device

def enable_two_factor(user):
    device = get_user_totp_device(user)
    if not device:
        device = user.totpdevice_set.create(confirmed=False)
    return device

def verify_two_factor(user, token):
    device = get_user_totp_device(user)
    if device and device.verify_token(token):
        device.confirmed = True
        device.save()
        return True
    return False

def disable_two_factor(user):
    device = get_user_totp_device(user)
    if device:
        device.delete()
        return True
    return False