from django.views.generic import TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from horoscopes.models import PersonalizedReading
from shop.models import Order

class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'users/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        context.update({
            'personalized_readings': PersonalizedReading.objects.filter(user=user).order_by('-created_at')[:5],
            'recent_orders': Order.objects.filter(user=user).order_by('-created_at')[:5],
            'zodiac_sign': user.zodiac_sign,
        })
        return context